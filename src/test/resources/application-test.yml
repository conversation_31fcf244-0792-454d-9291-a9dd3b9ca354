spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    database-platform: org.hibernate.dialect.H2Dialect
  
  liquibase:
    enabled: false
  
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest

logging:
  level:
    com.skilling.payment: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG

payment:
  environment: TEST
  jwt:
    signing-key: TEST_KEY
    ignore-expiration: true
  external-services:
    customer-service:
      base-url: http://localhost:8080
    trading-balance-service:
      base-url: http://localhost:8081
    trading-account-service:
      base-url: http://localhost:8082
    partner-service:
      base-url: http://localhost:8083
    exchange-rate-service:
      base-url: http://localhost:8084
    devcode-service:
      base-url: http://localhost:8085
