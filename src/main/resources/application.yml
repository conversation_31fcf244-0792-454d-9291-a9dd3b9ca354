server:
  port: ${PORT:3000}
  shutdown: graceful

spring:
  application:
    name: payment-service
  
  datasource:
    url: jdbc:postgresql://${DATABASE_HOST:payment-database}:${DATABASE_PORT:5432}/${DATABASE_NAME:payment}
    username: ${DATABASE_USER:payment}
    password: ${DATABASE_PASSWORD:payment}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: ${MAX_DB_CON_SIZE:5}
      minimum-idle: ${MIN_DB_CON_SIZE:1}
      idle-timeout: ${DB_CON_IDLE_TIME:10000}
      connection-timeout: 30000
      validation-timeout: 5000
      leak-detection-threshold: 60000

  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        jdbc:
          time_zone: UTC

  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.xml
    enabled: true

  rabbitmq:
    host: ${MESSAGING_HOST:rabbitmq}
    port: ${MESSAGING_PORT:5672}
    username: ${MESSAGING_USER:guest}
    password: ${MESSAGING_PASSWORD:guest}
    virtual-host: ${MESSAGING_VHOST:}
    listener:
      simple:
        prefetch: ${MSG_PREFETCH_COUNT:250}
        acknowledge-mode: manual
        retry:
          enabled: true
          initial-interval: 1000
          max-attempts: 3

  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${JWT_ISSUER_URI:}

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  health:
    rabbit:
      enabled: true

logging:
  level:
    com.skilling.payment: INFO
    org.springframework.amqp: WARN
    org.hibernate.SQL: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Application specific configuration
payment:
  environment: ${ENVIRONMENT_NAME:TEST}
  jwt:
    signing-key: ${JWT_PAYMENT_SIGNING_KEY:TEST_ofQk9OpNFm4A}
    ignore-expiration: ${IGNORE_JWT_PAYMENT_TOKEN_EXPIRATION:false}
  
  external-services:
    customer-service:
      base-url: http://${CUSTOMER_SERVICE_HOST:customer-service}:${CUSTOMER_SERVICE_PORT:3020}
    trading-balance-service:
      base-url: http://${TRADING_BALANCE_SERVICE_HOST:trading-balance-service}:${TRADING_BALANCE_SERVICE_PORT:3000}
    trading-account-service:
      base-url: http://${TAS_SERVICE_HOST:trading-account-service}:${TAS_SERVICE_PORT:3000}
    partner-service:
      base-url: http://${PARTNER_SERVICE_HOST:partner-service}:${PARTNER_SERVICE_PORT:3000}
    exchange-rate-service:
      base-url: http://${EXCHANGE_RATE_SERVICE_HOST:exchange-rate-service}:${EXCHANGE_RATE_SERVICE_PORT:3000}
    devcode-service:
      base-url: ${DEVCODE_SERVICE:https://test-api.paymentiq.io/paymentiq/api}
  
  devcode:
    merchant-id: ${DEVCODE_MERCHANT_ID:*********}
    seychelles-merchant-id: ${DEVCODE_SEYCHELLES_MERCHANT_ID:*********}
  
  praxis:
    domain: ${PRAXIS_DOMAIN:https://gateway.cashier-test.com}
    callback-url-root: ${PRAXIS_CALLBACK_URL_ROOT:https://devtest.skilling.com/g/payment/praxis}
    auth-header: ${PRAXIS_AUTH_HDR:gt-authentication}
    cancel-withdrawal-delay-ms: ${PRAXIS_CANCEL_WD_REQUEST_DELAY_MS:2000}
    fsa:
      merchant-id: ${PRAXIS_FSA_MERCHANT_ID:User-Tradingmoon}
      merchant-secret: ${PRAXIS_FSA_MERCHANT_SECRET:7MfRyS0f1BOsmZuvKDSrwGmv2Q9p9DVI}
      application-key: ${PRAXIS_FSA_APPLICATION_KEY:Tradingmoon TEST}
    cysec:
      merchant-id: ${PRAXIS_CYSEC_MERCHANT_ID:API-SkillingEU}
      merchant-secret: ${PRAXIS_CYSEC_MERCHANT_SECRET:v5TV6xze39MBf1NHcWWfxNgPH558tA5g}
      application-key: ${PRAXIS_CYSEC_APPLICATION_KEY:SkillingEU}

  payment-methods:
    country-blacklist:
      IT: [TRUSTLY]
      ES: [TRUSTLY]
    country-whitelist:
      KLARNA: [NO, DE, SE]
      SWISH: [SE]
    default-methods: [PAYPAL, BANK, TRUSTLY, VISA, MASTERCARD, KLARNA]
  
  fees:
    default-inactive-account-fee: ${DEFAULT_INACTIVE_ACCOUNT_FEE:5}
    currency-to-inactive-account-fee:
      EUR: 5
      USD: 5
      GBP: 4
      CHF: 5
      PLN: 20
      CZK: 120
      HUF: 1800

  messaging:
    enabled: ${MESSAGING_ENABLED:true}
    retry-attempts: ${MESSAGING_RETRY_ATTEMPTS:3}
    retry-delay-ms: ${MESSAGING_RETRY_DELAY_MS:5000}
    dead-letter-enabled: ${MESSAGING_DL_ENABLED:true}

---
spring:
  config:
    activate:
      on-profile: local
  
  datasource:
    url: ****************************************
    username: payment
    password: payment

logging:
  level:
    com.skilling.payment: DEBUG
    org.springframework.web: DEBUG
