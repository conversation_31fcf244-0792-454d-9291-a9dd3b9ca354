'use strict'

const expect = require('chai').expect
const rp = require('request-promise')
const httpStatusCodes = require('http-status-codes')
const uuid = require('uuid')
const mockServerClient = require('./utils/mock_server_client')
const config = require('../../lib/config')
const topology = require('./utils/test_rabbitmq_topology')

describe('Praxis notification call', () => {
  const customerId = '26647'
  const customerCid = '2003577'
  const paymentAccountId = Date.now().toString()
  const tradingAccountId = uuid.v4()

  const wdNotifBody = tid => ({
    merchant_id: 'User-Tradingmoon',
    application_key: 'Tradingmoon TEST',
    customer: {
      customer_token: '1234aaa'
    },
    session: {
      auth_token: '3388f0015845d534b0f853c4067e215a',
      intent: 'withdrawal',
      order_id: '3388f0015845d534b0f853c4067e215a',
      payment_method: 'Manual_BankWire',
      cid: customerCid,
      variable1: `a69cb580-c887-4ab5-a45c-a15895e921eb,1761-session-id,${customerId}`,
      variable2: null,
      variable3: null
    },
    transaction: {
      transaction_status: 'requested',
      transaction_type: 'payout',
      tid: tid,
      transaction_id: '7349717',
      currency: 'USD',
      amount: 10200,
      processed_currency: 'USD',
      processed_amount: 10200,
      created_by: 'INTERNET',
      edited_by: null,
      is_cascade: 0,
      cascade_level: null,
      payment_method: 'altbankwire',
      payment_processor: 'Manual_BankWire',
      withdrawal_request_id: null,
      card: {
        card_exp: null,
        card_number: null,
        card_type: null
      }
    },
    version: '1.3',
    timestamp: **********
  })

  const wdApprovalBody = tid => ({
    merchant_id: 'User-Tradingmoon',
    application_key: 'Tradingmoon TEST',
    customer: {
      customer_token: '1234aaa'
    },
    session: {
      auth_token: 'c53486ec9d21fb64613bee3bab09e3f4',
      intent: 'withdrawal',
      order_id: '3388f0015845d534b0f853c4067e215a',
      payment_method: 'Manual_BankWire',
      cid: customerCid,
      variable1: `a69cb580-c887-4ab5-a45c-a15895e921eb,1761-session-id,${customerId}`,
      variable2: null,
      variable3: null
    },
    transaction: {
      transaction_status: 'some-status-that-wont-be-used',
      transaction_type: 'payout',
      tid: tid,
      transaction_id: '7370937',
      currency: 'USD',
      amount: 10200,
      processed_currency: 'USD',
      processed_amount: 10200,
      created_by: 'INTERNET',
      edited_by: null,
      is_cascade: 0,
      cascade_level: null,
      payment_method: 'altbankwire',
      payment_processor: 'Manual_BankWire',
      withdrawal_request_id: tid,  // note this reference will be used for db-lookup
      card: {
        card_exp: null,
        card_number: null,
        card_type: null
      }
    },
    version: '1.3',
    timestamp: **********
  })

  before(() => {
    return rp({
      url: `http://payment-service:${config.PORT}/payment/customer/${customerId}/mapping`,
      method: 'POST',
      body: {
        externalId: customerCid,
        paymentAccountId,
        tradingAccountId
      },
      json: true
    })
  })

  beforeEach(() => {
    return Promise.all([
      mockServerClient.mockAnyResponse({
        httpRequest: {
          method: 'GET',
          path: `/v2/customers/${customerId}`
        },
        httpResponse: {
          statusCode: httpStatusCodes.OK,
          body: JSON.stringify({
            contactInformation: {
              country: 'ES'
            },
            personalInformation: {}
          })
        },
        times: {
          unlimited: true
        }
      }),
      mockServerClient.mockAnyResponse({
        httpRequest: {
          method: 'GET',
          path: `/latest`,  // exchange-rate-service
          queryStringParameters: {
            base: ['USD']
          }
        },
        httpResponse: {
          statusCode: httpStatusCodes.OK,
          body: JSON.stringify({
            rates: {
              EUR: 0.842045,
              USD: 1
            }
          })
        },
        times: {
          remainingTimes: 1,
          unlimited: false
        }
      })
    ])
  })

  const stubFindTxPraxisCall = (tid, txStatus = 'approved') => {
    mockServerClient.mockAnyResponse({
      httpRequest: {
        method: 'POST',
        path: '/agent/find-transaction'  // praxis
      },
      httpResponse: {
        statusCode: httpStatusCodes.OK,
        headers: {
          'Content-Type': ['application/json; charset=utf-8'],
          [config.PRAXIS.AUTH_HDR]: ['cb3df4ee1377e45633a6e5bc643f373b640cf6b36c5a113d5d805b9dae0047bb94f32dceee8a4aa85ebcdb9e859259f4']
        },
        body: JSON.stringify({
          merchant_id: 'User-Tradingmoon',
          application_key: 'Tradingmoon TEST',
          customer: {
            customer_token: '1234aaa'
          },
          transaction: {
            transaction_status: txStatus,
            transaction_type: 'payout',
            tid: tid,
            processed_currency: 'USD',
            processed_amount: 10200
          },
          status: 0,
          version: '1.3',
          timestamp: **********
        })
      },
      times: {
        remainingTimes: 2,
        unlimited: false
      }
    })
  }

  it('should ignore WD approval notification if given TX is already in "approved" status', function (done) {
    let transactionId
    const tid = ***********
    const prefixedTid = `p${tid}`
    const from = Date.now()
    let approvedMessagesConsumed = 0

    stubFindTxPraxisCall(tid)

    mockServerClient.mockAnyResponse({
      httpRequest: {
        method: 'GET',
        path: `/v1/customers/${customerId}/accounts/${tradingAccountId}/balance`  // TBS
      },
      httpResponse: {
        statusCode: httpStatusCodes.OK,
        body: JSON.stringify({ withdrawableAmount: 102.00 })
      },
      times: {
        remainingTimes: 1,
        unlimited: false
      }
    })

    this.rabbitChannel.consume(topology.REQUESTED_WITHDRAWAL_TEST_QUEUE.name, message => {
      const content = JSON.parse(message.content.toString())
      expect(content.customerId).to.equal(customerId)
      expect(content.data.currency).to.equal('USD')
      expect(content.data.currencySymbol).to.equal('$')
      expect(content.data.cardLastFourDigits).to.equal(null)
      expect(content.data.amount).to.equal('102.00')
      expect(content.data.status).to.equal('REQUESTED')
      expect(content.data.reference).to.equal(prefixedTid)
    }, { noAck: true, consumerTag: 'praxis-requested' })

    this.rabbitChannel.consume(topology.RELEASED_WITHDRAWAL_TEST_QUEUE.name, message => {
      if (++approvedMessagesConsumed !== 1) {
        throw new Error(`unexpected ${approvedMessagesConsumed}. tx released message received: ${JSON.stringify(message)}`)
      }
      const content = JSON.parse(message.content.toString())

      expect(content.customerId).to.equal(customerId)
      expect(content.data.currency).to.equal('USD')
      expect(content.data.currencySymbol).to.equal('$')
      expect(content.data.cardLastFourDigits).to.equal(null)
      expect(content.data.amount).to.equal('102.00')
      expect(content.data.status).to.equal('RELEASED')
      expect(content.data.reference).to.equal(prefixedTid)
    }, { noAck: true, consumerTag: 'praxis-released' })

    rp({
      url: `http://payment-service:${config.PORT}/payment/praxis/notification?licence=SEYCHELLES`,
      method: 'POST',
      body: wdNotifBody(tid),
      headers: {
        [config.PRAXIS.AUTH_HDR]: 'c9cfd722030109134a03e62eb5afefa762770a90b07e8ee85fe4973954c1fc1048b14d81fb9c1092e8f70e04995263e6',
        'Content-Type': 'application/json; charset=utf-8'
      },
      json: true
    })
      .then(wdRequestResponse => {
        expect(wdRequestResponse.status).to.equal(0)
        expect(wdRequestResponse.description).to.equal('Ok')

        const to = Date.now()

        return rp({
          url: `http://payment-service:${config.PORT}/payment/bo/customer/${customerId}/transactions?from=${from}&to=${to}&page=1&perPage=1`,
          method: 'GET',
          json: true
        })
      })
      .then(transactionListResponse => {
        const transaction = transactionListResponse.rows[0]

        transactionId = transaction.id

        expect(transaction.amount).to.equal('102.00')
        expect(transaction.currency).to.equal('USD')
        expect(transaction.currencySymbol).to.equal('$')
        expect(transaction.cardLastFourDigits).to.equal(null)
        expect(transaction.transactionType).to.equal('WD')
        expect(transaction.status).to.equal('REQUESTED')
        expect(transaction.reference).to.equal(prefixedTid)

        return rp({
          url: `http://payment-service:${config.PORT}/payment/praxis/notification?licence=SEYCHELLES`,
          method: 'POST',
          body: wdApprovalBody(tid),
          headers: {
            [config.PRAXIS.AUTH_HDR]: '9b4d4b5a8c25d0f3a9031fab2ff93c8f49e8deb676f46d236a7d469dcfd563b04efb2b70c5c505939b3977ce5f101430',
            'Content-Type': 'application/json; charset=utf-8'
          },
          json: true
        })
      })
      .then(wdApprovalResponse => {
        expect(wdApprovalResponse.status).to.equal(0)
        expect(wdApprovalResponse.description).to.equal('Ok')

        return rp({
          url: `http://payment-service:${config.PORT}/payment/bo/transactions/${transactionId}`,
          method: 'GET',
          json: true
        })
      })
      .then(transaction => {
        expect(transaction.amount).to.equal('102.00')
        expect(transaction.currency).to.equal('USD')
        expect(transaction.transactionType).to.equal('WD')
        expect(transaction.status).to.equal('RELEASED')
        expect(transaction.reference).to.equal(prefixedTid)

        // !! send tx update notification w/ same status again:
        // ///////////////////////////////////////////////////////
        return rp({
          url: `http://payment-service:${config.PORT}/payment/praxis/notification?licence=SEYCHELLES`,
          method: 'POST',
          body: wdApprovalBody(tid),
          headers: {
            [config.PRAXIS.AUTH_HDR]: '9b4d4b5a8c25d0f3a9031fab2ff93c8f49e8deb676f46d236a7d469dcfd563b04efb2b70c5c505939b3977ce5f101430',
            'Content-Type': 'application/json; charset=utf-8'
          },
          json: true
        })
      })
      .then(wdApprovalResponse => {
        expect(wdApprovalResponse.status).to.equal(0)
        expect(wdApprovalResponse.description).to.equal('Ok')

        return rp({
          url: `http://payment-service:${config.PORT}/payment/bo/transactions/${transactionId}`,
          method: 'GET',
          json: true
        })
      })
      .then(transaction => {
        expect(transaction.amount).to.equal('102.00')
        expect(transaction.currency).to.equal('USD')
        expect(transaction.transactionType).to.equal('WD')
        expect(transaction.status).to.equal('RELEASED')
        expect(transaction.reference).to.equal(prefixedTid)
      })
      .then(() =>
        Promise.all([
          this.rabbitChannel.cancel('praxis-requested'),
          this.rabbitChannel.cancel('praxis-released')
        ]))
      .then(() => done())
      .catch(done)
  })
})
