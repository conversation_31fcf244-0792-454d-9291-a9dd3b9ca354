plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.6'
    id 'io.spring.dependency-management' version '1.1.6'
    id 'org.liquibase.gradle' version '2.2.2'
}

group = 'com.skilling'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

dependencies {
    // Spring Boot Starters
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-amqp'
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    
    // Database
    implementation 'org.postgresql:postgresql'
    implementation 'org.liquibase:liquibase-core'
    
    // JSON Processing
    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
    
    // JWT
    implementation 'io.jsonwebtoken:jjwt-api:0.12.6'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.12.6'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.12.6'
    
    // Utilities
    implementation 'org.apache.commons:commons-lang3:3.17.0'
    implementation 'org.apache.commons:commons-collections4:4.4'
    implementation 'com.google.guava:guava:33.3.1-jre'
    
    // Logging
    implementation 'net.logstash.logback:logstash-logback-encoder:8.0'
    
    // Resilience
    implementation 'io.github.resilience4j:resilience4j-spring-boot3:2.2.0'
    
    // BigDecimal math
    implementation 'ch.obermuhlner:big-math:2.3.2'
    
    // Configuration
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    
    // Development
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    
    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.amqp:spring-rabbit-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'org.testcontainers:junit-jupiter'
    testImplementation 'org.testcontainers:postgresql'
    testImplementation 'org.testcontainers:rabbitmq'
    testImplementation 'com.github.tomakehurst:wiremock-jre8:3.0.1'
    testImplementation 'org.awaitility:awaitility:4.2.2'
    testImplementation 'com.h2database:h2'
    
    // Liquibase
    liquibaseRuntime 'org.liquibase:liquibase-core'
    liquibaseRuntime 'org.postgresql:postgresql'
    liquibaseRuntime 'info.picocli:picocli:4.7.6'
}

tasks.named('test') {
    useJUnitPlatform()
    testLogging {
        events "passed", "skipped", "failed"
    }
}

liquibase {
    activities {
        main {
            changelogFile 'src/main/resources/db/changelog/db.changelog-master.xml'
            url project.findProperty('liquibase.url') ?: '****************************************'
            username project.findProperty('liquibase.username') ?: 'payment'
            password project.findProperty('liquibase.password') ?: 'payment'
        }
    }
}

bootRun {
    jvmArgs = ['-Dspring.profiles.active=local']
}

jar {
    enabled = false
}

bootJar {
    archiveFileName = 'payment-service.jar'
}
